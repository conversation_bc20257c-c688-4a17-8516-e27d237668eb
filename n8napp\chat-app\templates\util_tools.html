<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UTIL Tools - Grupo Alves</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            height: 100vh;
            overflow: hidden;
        }

        .util-container {
            display: flex;
            height: 100vh;
            background: white;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1000;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            position: relative;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 20px 10px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .logo {
            font-size: 18px;
        }

        .subtitle {
            font-size: 14px;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .subtitle {
            display: none;
        }

        .collapse-btn {
            position: absolute;
            top: 20px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .collapse-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar.collapsed .collapse-btn {
            right: 50%;
            transform: translateX(50%);
        }

        /* Navigation Styles */
        .nav-section {
            padding: 20px 0;
        }

        .nav-title {
            padding: 0 20px 10px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .nav-title {
            display: none;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .sidebar.collapsed .nav-item {
            padding: 15px;
            justify-content: center;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: rgba(255,255,255,0.5);
        }

        .nav-item.active {
            background: rgba(255,255,255,0.15);
            border-left-color: white;
        }

        .nav-item i {
            font-size: 18px;
            width: 24px;
            text-align: center;
            margin-right: 15px;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .nav-item i {
            margin-right: 0;
            font-size: 20px;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .nav-text {
            display: none;
        }

        .nav-badge {
            background: #ff4757;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: auto;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .nav-badge {
            display: none;
        }

        /* Tooltip for collapsed sidebar */
        .nav-tooltip {
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .nav-tooltip::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: #333;
        }

        .sidebar.collapsed .nav-item:hover .nav-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #f5f7fa;
        }

        .content-header {
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e0e6ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .content-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .content-title i {
            color: #667eea;
        }

        .content-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
        }

        /* Content Body */
        .content-body {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .module-content {
            display: none;
        }

        .module-content.active {
            display: block;
        }

        /* Welcome Screen */
        .welcome-screen {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .welcome-icon {
            font-size: 80px;
            color: #667eea;
            margin-bottom: 30px;
        }

        .welcome-title {
            font-size: 32px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            background: white;
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }

        .feature-icon {
            font-size: 40px;
            color: #667eea;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .feature-description {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.5;
        }

        /* Status Indicators */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online {
            background: #2ecc71;
            box-shadow: 0 0 0 2px rgba(46, 204, 113, 0.3);
        }

        .status-offline {
            background: #e74c3c;
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.3);
        }

        .status-warning {
            background: #f39c12;
            box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            
            .sidebar .nav-text,
            .sidebar .nav-title,
            .sidebar .subtitle {
                display: none;
            }
            
            .content-header {
                padding: 15px 20px;
            }
            
            .content-title {
                font-size: 24px;
            }
            
            .content-body {
                padding: 20px;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="util-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">UTIL Tools</div>
                <div class="subtitle">Grupo Alves</div>
                <button class="collapse-btn" onclick="toggleSidebar()">
                    <i class="fas fa-chevron-left" id="collapseIcon"></i>
                </button>
            </div>

            <div class="nav-section">
                <div class="nav-title">Principal</div>
                
                <div class="nav-item active" onclick="showModule('welcome')" data-module="welcome">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Início</span>
                    <div class="nav-tooltip">Início</div>
                </div>

                <div class="nav-item" onclick="showModule('chat')" data-module="chat">
                    <i class="fas fa-comments"></i>
                    <span class="nav-text">Chat IA</span>
                    <span class="status-indicator status-online"></span>
                    <div class="nav-tooltip">Chat com IA</div>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">Ferramentas</div>
                
                <div class="nav-item" onclick="showModule('search')" data-module="search">
                    <i class="fas fa-search"></i>
                    <span class="nav-text">Pesquisa Web</span>
                    <div class="nav-tooltip">Pesquisa Web</div>
                </div>

                <div class="nav-item" onclick="showModule('sql')" data-module="sql">
                    <i class="fas fa-database"></i>
                    <span class="nav-text">SQL Assistant</span>
                    <div class="nav-tooltip">SQL Assistant</div>
                </div>

                <div class="nav-item" onclick="showModule('email')" data-module="email">
                    <i class="fas fa-envelope"></i>
                    <span class="nav-text">Email IA</span>
                    <span class="nav-badge">3</span>
                    <div class="nav-tooltip">Sistema de Email</div>
                </div>

                <div class="nav-item" onclick="showModule('crm')" data-module="crm">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">CRM</span>
                    <div class="nav-tooltip">CRM Inteligente</div>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">Sistema</div>
                
                <div class="nav-item" onclick="showModule('help')" data-module="help">
                    <i class="fas fa-question-circle"></i>
                    <span class="nav-text">Ajuda</span>
                    <div class="nav-tooltip">Central de Ajuda</div>
                </div>

                <div class="nav-item" onclick="showModule('api')" data-module="api">
                    <i class="fas fa-code"></i>
                    <span class="nav-text">API Docs</span>
                    <div class="nav-tooltip">Documentação da API</div>
                </div>

                <div class="nav-item" onclick="showModule('config')" data-module="config">
                    <i class="fas fa-cog"></i>
                    <span class="nav-text">Configurações</span>
                    <div class="nav-tooltip">Configurações</div>
                </div>

                <div class="nav-item" onclick="window.open('http://localhost:4000', '_blank')" data-module="n8n">
                    <i class="fas fa-project-diagram"></i>
                    <span class="nav-text">n8n Automação</span>
                    <i class="fas fa-external-link-alt" style="font-size: 12px; margin-left: auto;"></i>
                    <div class="nav-tooltip">n8n Automação</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <div class="content-title">
                    <i class="fas fa-home" id="contentIcon"></i>
                    <span id="contentTitle">Bem-vindo ao UTIL Tools</span>
                </div>
                <div class="content-actions">
                    <button class="btn btn-secondary" onclick="refreshContent()">
                        <i class="fas fa-sync-alt"></i>
                        Atualizar
                    </button>
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-comments"></i>
                        Chat Principal
                    </a>
                </div>
            </div>

            <div class="content-body">
                <!-- Welcome Module -->
                <div id="welcome" class="module-content active">
                    <div class="welcome-screen fade-in">
                        <div class="welcome-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h1 class="welcome-title">UTIL Tools</h1>
                        <p class="welcome-subtitle">
                            Central unificada de ferramentas de IA e automação para Grupo Alves.<br>
                            Selecione uma ferramenta no menu lateral para começar.
                        </p>

                        <div class="feature-grid">
                            <div class="feature-card" onclick="showModule('chat')">
                                <div class="feature-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div class="feature-title">Chat IA</div>
                                <div class="feature-description">
                                    Conversação inteligente com pesquisa automática e memória persistente
                                </div>
                            </div>

                            <div class="feature-card" onclick="showModule('search')">
                                <div class="feature-icon">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div class="feature-title">Pesquisa Web</div>
                                <div class="feature-description">
                                    Busca avançada na web com análise de conteúdo e exportação
                                </div>
                            </div>

                            <div class="feature-card" onclick="showModule('sql')">
                                <div class="feature-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="feature-title">SQL Assistant</div>
                                <div class="feature-description">
                                    Geração de consultas SQL através de linguagem natural
                                </div>
                            </div>

                            <div class="feature-card" onclick="showModule('email')">
                                <div class="feature-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="feature-title">Email IA</div>
                                <div class="feature-description">
                                    Sistema de email com assistente de resposta inteligente
                                </div>
                            </div>

                            <div class="feature-card" onclick="showModule('crm')">
                                <div class="feature-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="feature-title">CRM Inteligente</div>
                                <div class="feature-description">
                                    Gestão de relacionamento com análise automática de clientes
                                </div>
                            </div>

                            <div class="feature-card" onclick="showModule('help')">
                                <div class="feature-icon">
                                    <i class="fas fa-question-circle"></i>
                                </div>
                                <div class="feature-title">Central de Ajuda</div>
                                <div class="feature-description">
                                    Documentação completa e guias de uso do sistema
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other modules will be loaded here -->
                <div id="chat" class="module-content">
                    <iframe src="/" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>

                <div id="search" class="module-content">
                    <iframe src="/search" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>

                <div id="sql" class="module-content">
                    <iframe src="/sql-assistant" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>

                <div id="email" class="module-content">
                    <iframe src="/email" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>

                <div id="crm" class="module-content">
                    <iframe src="/crm" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>

                <div id="help" class="module-content">
                    <iframe src="/help" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>

                <div id="api" class="module-content">
                    <iframe src="/api-docs" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>

                <div id="config" class="module-content">
                    <iframe src="/config" width="100%" height="100%" frameborder="0" style="border-radius: 10px;"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();

        // Module configuration
        const modules = {
            welcome: { title: 'Bem-vindo ao UTIL Tools', icon: 'fas fa-home' },
            chat: { title: 'Chat IA', icon: 'fas fa-comments' },
            search: { title: 'Pesquisa Web', icon: 'fas fa-search' },
            sql: { title: 'SQL Assistant', icon: 'fas fa-database' },
            email: { title: 'Email IA', icon: 'fas fa-envelope' },
            crm: { title: 'CRM Inteligente', icon: 'fas fa-users' },
            help: { title: 'Central de Ajuda', icon: 'fas fa-question-circle' },
            api: { title: 'Documentação da API', icon: 'fas fa-code' },
            config: { title: 'Configurações', icon: 'fas fa-cog' }
        };

        // Sidebar toggle functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const icon = document.getElementById('collapseIcon');
            
            sidebar.classList.toggle('collapsed');
            
            if (sidebar.classList.contains('collapsed')) {
                icon.className = 'fas fa-chevron-right';
            } else {
                icon.className = 'fas fa-chevron-left';
            }
        }

        // Module switching functionality
        function showModule(moduleId) {
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            const activeItem = document.querySelector(`[data-module="${moduleId}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }

            // Update content
            document.querySelectorAll('.module-content').forEach(content => {
                content.classList.remove('active');
            });
            
            const activeContent = document.getElementById(moduleId);
            if (activeContent) {
                activeContent.classList.add('active');
                activeContent.classList.add('fade-in');
            }

            // Update header
            const moduleConfig = modules[moduleId];
            if (moduleConfig) {
                document.getElementById('contentTitle').textContent = moduleConfig.title;
                document.getElementById('contentIcon').className = moduleConfig.icon;
            }

            // Store current module
            localStorage.setItem('util_current_module', moduleId);
        }

        // Refresh content
        function refreshContent() {
            const currentModule = localStorage.getItem('util_current_module') || 'welcome';
            const iframe = document.querySelector(`#${currentModule} iframe`);
            
            if (iframe) {
                iframe.src = iframe.src;
            } else {
                location.reload();
            }
        }

        // Socket event listeners
        socket.on('connect', function() {
            console.log('Connected to UTIL Tools');
        });

        socket.on('notification', function(data) {
            showNotification(data.message, data.type);
        });

        // Notification system
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#e74c3c' : type === 'success' ? '#2ecc71' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved module or default to welcome
            const savedModule = localStorage.getItem('util_current_module') || 'welcome';
            showModule(savedModule);

            // Auto-collapse sidebar on mobile
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('collapseIcon').className = 'fas fa-chevron-right';
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('collapseIcon').className = 'fas fa-chevron-right';
            }
        });
    </script>
</body>
</html>
