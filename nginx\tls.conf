﻿server {
  listen 443 ssl http2;
  server_name localhost;
  ssl_certificate     /etc/nginx/certs/localhost.pem;
  ssl_certificate_key /etc/nginx/certs/localhost-key.pem;

  root /usr/share/nginx/html; index index.html;
  location /api/ {
    proxy_pass http://flask:8000/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
  }
}
