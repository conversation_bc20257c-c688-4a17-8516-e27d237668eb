<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bases de Dados Externas - Grupo Alves</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .database-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: grid;
            grid-template-columns: 300px 1fr;
            height: 90vh;
        }

        .sidebar {
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
        }

        .nav-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 1px solid #e0e0e0;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-item:hover, .nav-item.active {
            background: #e3f2fd;
            color: #1976d2;
        }

        .main-content {
            display: flex;
            flex-direction: column;
        }

        .content-header {
            background: white;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: calc(90vh - 80px);
        }

        .db-section {
            display: none;
        }

        .db-section.active {
            display: block;
        }

        .query-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s;
            font-family: 'Courier New', monospace;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .results-container {
            display: none;
            max-height: 60vh;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: white;
            padding: 15px;
        }

        .results-container.show {
            display: block;
        }

        .result-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .result-table th {
            background: #667eea;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }

        .result-table td {
            padding: 12px;
            border-bottom: 1px solid #e0e0e0;
        }

        .result-table tr:hover {
            background: #f5f5f5;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading.show {
            display: block;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            display: none;
        }

        .notification.error {
            background: #f44336;
        }

        .notification.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }

        .schema-tree {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-item {
            padding: 10px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            transition: background 0.3s;
        }

        .table-item:hover {
            background: #f5f5f5;
        }

        .table-name {
            font-weight: 600;
            color: #1976d2;
        }

        .table-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .context-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .context-query {
            font-family: 'Courier New', monospace;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .context-meta {
            font-size: 12px;
            color: #666;
        }

        .sql-examples {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .example-query {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .example-query:hover {
            background: #f0f0f0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="database-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🗄️ Bases de Dados</h2>
                <p>MCP Integration</p>
            </div>
            <div class="sidebar-nav">
                <div class="nav-item active" onclick="showSection('query')">
                    📝 Consultas SQL
                </div>
                <div class="nav-item" onclick="showSection('schema')">
                    📋 Schema
                </div>
                <div class="nav-item" onclick="showSection('context')">
                    🧠 Contexto
                </div>
                <div class="nav-item" onclick="showSection('examples')">
                    💡 Exemplos
                </div>
            </div>
        </div>

        <div class="main-content">

            <div class="content-body">
                <!-- Query Section -->
                <div id="query" class="db-section active">
                    <div class="query-form">
                        <div class="form-group">
                            <label class="form-label">Consulta SQL:</label>
                            <textarea class="form-textarea" id="sqlQuery" placeholder="SELECT * FROM tabela WHERE condicao = 'valor'"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Tipo de Banco:</label>
                            <select class="form-select" id="dbType">
                                <option value="auto">Detectar automaticamente</option>
                                <option value="postgresql">PostgreSQL</option>
                                <option value="mysql">MySQL</option>
                                <option value="sqlite">SQLite</option>
                                <option value="mongodb">MongoDB</option>
                            </select>
                        </div>

                        <button class="btn btn-primary" onclick="executeQuery()">🚀 Executar Consulta</button>
                        <button class="btn btn-secondary" onclick="clearQuery()">🗑️ Limpar</button>
                    </div>

                    <div class="loading" id="queryLoading">
                        🗄️ Executando consulta...
                    </div>

                    <div class="results-container" id="queryResults"></div>
                </div>

                <!-- Schema Section -->
                <div id="schema" class="db-section">
                    <div class="query-form">
                        <div class="form-group">
                            <label class="form-label">Nome do Banco (opcional):</label>
                            <input type="text" class="form-input" id="databaseName" placeholder="nome_do_banco">
                        </div>

                        <button class="btn btn-primary" onclick="loadSchema()">📋 Carregar Schema</button>
                    </div>

                    <div class="loading" id="schemaLoading">
                        📋 Carregando schema...
                    </div>

                    <div class="results-container" id="schemaResults"></div>
                </div>

                <!-- Context Section -->
                <div id="context" class="db-section">
                    <div class="loading" id="contextLoading">Carregando contexto...</div>
                    <div id="contextResults"></div>
                </div>

                <!-- Examples Section -->
                <div id="examples" class="db-section">
                    <div class="sql-examples">
                        <h3>Exemplos de Consultas SQL</h3>
                        <p>Clique em qualquer exemplo para usar:</p>
                        
                        <div class="example-query" onclick="useExample(this)">
                            SELECT * FROM usuarios LIMIT 10;
                        </div>
                        <div class="example-query" onclick="useExample(this)">
                            SELECT COUNT(*) FROM pedidos WHERE data_pedido >= '2024-01-01';
                        </div>
                        <div class="example-query" onclick="useExample(this)">
                            SELECT u.nome, COUNT(p.id) as total_pedidos 
                            FROM usuarios u 
                            LEFT JOIN pedidos p ON u.id = p.usuario_id 
                            GROUP BY u.id, u.nome 
                            ORDER BY total_pedidos DESC;
                        </div>
                        <div class="example-query" onclick="useExample(this)">
                            SELECT produto, SUM(quantidade) as total_vendido 
                            FROM vendas 
                            WHERE data_venda >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
                            GROUP BY produto 
                            ORDER BY total_vendido DESC;
                        </div>
                        <div class="example-query" onclick="useExample(this)">
                            UPDATE produtos SET preco = preco * 1.1 WHERE categoria = 'eletrônicos';
                        </div>
                        <div class="example-query" onclick="useExample(this)">
                            INSERT INTO clientes (nome, email, telefone) VALUES ('João Silva', '<EMAIL>', '11999999999');
                        </div>
                    </div>

                    <div class="sql-examples">
                        <h3>Comandos do Chat</h3>
                        <p>Use estes comandos no chat principal:</p>
                        
                        <div class="example-query">
                            /db SELECT * FROM usuarios WHERE ativo = 1
                        </div>
                        <div class="example-query">
                            /schema nome_do_banco
                        </div>
                        <div class="example-query">
                            /db INSERT INTO produtos (nome, preco) VALUES ('Produto X', 99.99)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        let currentSessionId = localStorage.getItem('db_session_id') || generateUUID();
        localStorage.setItem('db_session_id', currentSessionId);

        // Socket event listeners
        socket.on('database_query_completed', function(data) {
            showNotification(`Consulta executada: ${data.results_count} resultados em ${data.execution_time}ms`, 'success');
        });

        // Utility functions
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }

        // Navigation
        function showSection(section) {
            // Update nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update content
            document.querySelectorAll('.db-section').forEach(el => {
                el.classList.remove('active');
            });
            document.getElementById(section).classList.add('active');

            // Update title
            const titles = {
                'query': 'Consultas SQL',
                'schema': 'Schema do Banco',
                'context': 'Contexto da Sessão',
                'examples': 'Exemplos e Comandos'
            };
            document.getElementById('sectionTitle').textContent = titles[section];

            // Load data if needed
            if (section === 'context') {
                loadContext();
            }
        }

        // Execute SQL query
        async function executeQuery() {
            const query = document.getElementById('sqlQuery').value.trim();
            const dbType = document.getElementById('dbType').value;

            if (!query) {
                showNotification('Por favor, digite uma consulta SQL', 'error');
                return;
            }

            const loading = document.getElementById('queryLoading');
            const results = document.getElementById('queryResults');
            
            loading.classList.add('show');
            results.classList.remove('show');

            try {
                const response = await fetch('/api/database/query', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query: query,
                        db_type: dbType,
                        session_id: currentSessionId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    displayQueryResults(data);
                    results.classList.add('show');
                } else {
                    showNotification('Erro na consulta: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Erro de conexão: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Display query results
        function displayQueryResults(data) {
            const container = document.getElementById('queryResults');
            
            let html = `
                <div style="margin-bottom: 20px;">
                    <strong>Resultados:</strong> ${data.results.length} registros encontrados<br>
                    <strong>Tempo de execução:</strong> ${data.execution_time}ms<br>
                    <strong>Tipo de consulta:</strong> ${data.query_type}
                </div>
            `;

            if (data.results.length > 0) {
                html += '<table class="result-table">';
                
                // Headers
                const firstRow = data.results[0];
                if (typeof firstRow === 'object') {
                    html += '<thead><tr>';
                    Object.keys(firstRow).forEach(key => {
                        html += `<th>${key}</th>`;
                    });
                    html += '</tr></thead>';
                    
                    // Rows
                    html += '<tbody>';
                    data.results.forEach(row => {
                        html += '<tr>';
                        Object.values(row).forEach(value => {
                            html += `<td>${value !== null ? value : 'NULL'}</td>`;
                        });
                        html += '</tr>';
                    });
                    html += '</tbody>';
                } else {
                    // Simple results
                    html += '<thead><tr><th>Resultado</th></tr></thead>';
                    html += '<tbody>';
                    data.results.forEach(result => {
                        html += `<tr><td>${result}</td></tr>`;
                    });
                    html += '</tbody>';
                }
                
                html += '</table>';
            } else {
                html += '<p style="text-align: center; color: #666; padding: 40px;">Nenhum resultado encontrado</p>';
            }

            container.innerHTML = html;
        }

        // Load database schema
        async function loadSchema() {
            const dbName = document.getElementById('databaseName').value.trim();
            const loading = document.getElementById('schemaLoading');
            const results = document.getElementById('schemaResults');
            
            loading.classList.add('show');
            results.classList.remove('show');

            try {
                const url = `/api/database/schema?session_id=${currentSessionId}${dbName ? '&database=' + dbName : ''}`;
                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    displaySchemaResults(data);
                    results.classList.add('show');
                } else {
                    showNotification('Erro ao carregar schema: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Erro de conexão: ' + error.message, 'error');
            } finally {
                loading.classList.remove('show');
            }
        }

        // Display schema results
        function displaySchemaResults(data) {
            const container = document.getElementById('schemaResults');
            
            let html = '<div class="schema-tree">';
            
            if (data.tables && data.tables.length > 0) {
                html += '<h3>Tabelas Disponíveis:</h3>';
                data.tables.forEach(table => {
                    html += '<div class="table-item">';
                    if (typeof table === 'object') {
                        html += `<div class="table-name">${table.name || 'N/A'}</div>`;
                        html += `<div class="table-info">Registros: ${table.rows || 0} | Colunas: ${table.columns || 0}</div>`;
                    } else {
                        html += `<div class="table-name">${table}</div>`;
                    }
                    html += '</div>';
                });
            } else {
                html += '<p>Nenhuma tabela encontrada</p>';
            }
            
            if (data.relationships && data.relationships.length > 0) {
                html += `<h3 style="margin-top: 20px;">Relacionamentos: ${data.relationships.length}</h3>`;
            }
            
            html += '</div>';
            container.innerHTML = html;
        }

        // Load context
        async function loadContext() {
            const loading = document.getElementById('contextLoading');
            const results = document.getElementById('contextResults');
            
            loading.style.display = 'block';

            try {
                const response = await fetch(`/api/database/context?session_id=${currentSessionId}&limit=20`);
                const data = await response.json();

                if (data.success) {
                    results.innerHTML = '';
                    
                    if (data.contexts.length === 0) {
                        results.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">Nenhum contexto de banco de dados encontrado</p>';
                    } else {
                        data.contexts.forEach(context => {
                            const contextDiv = document.createElement('div');
                            contextDiv.className = 'context-item';
                            contextDiv.innerHTML = `
                                <div><strong>Fonte:</strong> ${context.data_source}</div>
                                <div class="context-query">${context.query}</div>
                                <div class="context-meta">
                                    ${new Date(context.timestamp).toLocaleString('pt-BR')} • 
                                    ${context.results.results ? context.results.results.length + ' resultados' : 'Sem resultados'}
                                </div>
                            `;
                            results.appendChild(contextDiv);
                        });
                    }
                }
            } catch (error) {
                showNotification('Erro ao carregar contexto: ' + error.message, 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        // Use example query
        function useExample(element) {
            const query = element.textContent.trim();
            document.getElementById('sqlQuery').value = query;
            showSection('query');
            showNotification('Exemplo carregado! Clique em "Executar Consulta"', 'success');
        }

        // Clear query
        function clearQuery() {
            document.getElementById('sqlQuery').value = '';
            document.getElementById('queryResults').classList.remove('show');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus on SQL query textarea
            document.getElementById('sqlQuery').focus();
        });
    </script>
</body>
</html>
