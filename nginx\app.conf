﻿server {
  listen 80 default_server;
  server_name _;

  # estático opcional em /
  root /usr/share/nginx/html;
  index index.html;

  # proxy do /api/ → Flask
  location /api/ {
    proxy_pass         http://flask:8000/;
    proxy_http_version 1.1;
    proxy_set_header   Host              $host;
    proxy_set_header   X-Real-IP         $remote_addr;
    proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
    proxy_set_header   X-Forwarded-Proto $scheme;

    client_max_body_size 25m;
    proxy_connect_timeout 5s;
    proxy_send_timeout    60s;
    proxy_read_timeout    60s;
  }
}
