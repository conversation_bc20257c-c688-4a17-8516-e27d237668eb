version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=America/Sao_Paulo
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
    networks:
      - n8n-network

  echo-service:
    image: mendhak/http-https-echo:latest
    container_name: echo-service
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - HTTP_PORT=8080
      - HTTPS_PORT=8443
    networks:
      - n8n-network

volumes:
  n8n_data:

networks:
  n8n-network:
    driver: bridge
