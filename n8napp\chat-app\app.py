from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import requests
import json
import os

app = Flask(__name__)
CORS(app)

# Configuração da LLM
LLM_HOST = os.getenv('LLM_HOST', 'localhost')
LLM_PORT = os.getenv('LLM_PORT', '11434')
LLM_URL = f"http://{LLM_HOST}:{LLM_PORT}/api/generate"

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'Mensagem não pode estar vazia'}), 400
        
        # Payload para a LLM
        llm_payload = {
            "model": "llama3.2:1b",
            "prompt": user_message,
            "stream": False
        }
        
        # Fazer requisição para a LLM
        response = requests.post(LLM_URL, json=llm_payload, timeout=30)
        
        if response.status_code == 200:
            llm_response = response.json()
            bot_message = llm_response.get('response', 'Desculpe, não consegui processar sua mensagem.')
            
            return jsonify({
                'success': True,
                'message': bot_message
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Erro na LLM: {response.status_code}'
            }), 500
            
    except requests.exceptions.RequestException as e:
        return jsonify({
            'success': False,
            'error': f'Erro de conexão com a LLM: {str(e)}'
        }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erro interno: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health():
    try:
        # Testar conexão com a LLM
        test_payload = {
            "model": "llama3.2:1b",
            "prompt": "test",
            "stream": False
        }
        response = requests.post(LLM_URL, json=test_payload, timeout=5)
        llm_status = "online" if response.status_code == 200 else "offline"
    except:
        llm_status = "offline"
    
    return jsonify({
        'status': 'online',
        'llm_status': llm_status,
        'llm_url': LLM_URL
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8000, debug=True)
