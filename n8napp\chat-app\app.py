from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
import requests
import json
import os
import uuid
from datetime import datetime
import sqlite3
from threading import Lock, Thread
import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from email.header import decode_header
import time
import ssl
from bs4 import BeautifulSoup
import asyncio
import aiohttp
from urllib.parse import urljoin, urlparse
import re

app = Flask(__name__)
CORS(app)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
socketio = SocketIO(app, cors_allowed_origins="*")

# Database lock for thread safety
db_lock = Lock()

# Configurações padrão
DEFAULT_CONFIG = {
    'provider': 'openrouter',  # Iniciar com gratuito
    'ollama_host': os.getenv('LLM_HOST', 'host.docker.internal'),
    'ollama_port': os.getenv('LLM_PORT', '11434'),
    'ollama_model': 'llama3.2:1b',
    'openrouter_api_key': '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',
    'openrouter_model': 'google/gemma-2-9b-it:free',  # Modelo gratuito
    'max_conversation_length': 20,  # Maximum messages to keep in memory
    'conversation_summary_threshold': 15  # When to start summarizing
}

# Configurações de Email
EMAIL_CONFIG = {
    'smtp_server': 'smtp.uni5.net',
    'smtp_port': 587,
    'imap_server': 'imap.uni5.net',
    'imap_port': 143,
    'email': '<EMAIL>',
    'password': '123Leo456@7',
    'use_tls': True
}

# Configurações do Tavily AI (Web Search & Scraping)
TAVILY_CONFIG = {
    'api_key': 'cac6b7c6-516b-4360-93b5-a05d1c2e0dae',
    'base_url': 'https://api.tavily.com/search',
    'max_results': 10,
    'include_domains': [],
    'exclude_domains': [],
    'search_depth': 'basic'  # basic, advanced
}

# Configuração atual (em memória, pode ser salva em arquivo depois)
current_config = DEFAULT_CONFIG.copy()

# Database path
DB_PATH = '/app/data/conversations.db'

# Ensure data directory exists
os.makedirs('/app/data', exist_ok=True)

# Initialize database
def init_db():
    """Initialize SQLite database for conversation and email storage"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                provider TEXT,
                model TEXT
            )
        ''')
        conn.execute('''
            CREATE TABLE IF NOT EXISTS conversation_summaries (
                session_id TEXT PRIMARY KEY,
                summary TEXT NOT NULL,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.execute('''
            CREATE TABLE IF NOT EXISTS emails (
                id TEXT PRIMARY KEY,
                message_id TEXT UNIQUE,
                subject TEXT NOT NULL,
                sender TEXT NOT NULL,
                recipient TEXT NOT NULL,
                body TEXT NOT NULL,
                html_body TEXT,
                status TEXT DEFAULT 'draft',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                sent_at DATETIME,
                received_at DATETIME,
                session_id TEXT,
                llm_generated BOOLEAN DEFAULT 0
            )
        ''')
        conn.execute('''
            CREATE TABLE IF NOT EXISTS email_monitoring (
                id TEXT PRIMARY KEY,
                email_id TEXT NOT NULL,
                check_type TEXT NOT NULL,
                status TEXT NOT NULL,
                details TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (email_id) REFERENCES emails (id)
            )
        ''')
        conn.execute('''
            CREATE TABLE IF NOT EXISTS web_searches (
                id TEXT PRIMARY KEY,
                query TEXT NOT NULL,
                results TEXT NOT NULL,
                search_type TEXT DEFAULT 'web',
                session_id TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                results_count INTEGER DEFAULT 0
            )
        ''')
        conn.execute('''
            CREATE TABLE IF NOT EXISTS scraped_content (
                id TEXT PRIMARY KEY,
                url TEXT NOT NULL,
                title TEXT,
                content TEXT NOT NULL,
                metadata TEXT,
                session_id TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                content_type TEXT DEFAULT 'html'
            )
        ''')
        conn.commit()

# Initialize database on startup
init_db()

# Conversation Management Functions
def get_session_id():
    """Get or create session ID for conversation tracking"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    return session['session_id']

def save_message(session_id, role, content, provider=None, model=None):
    """Save a message to the conversation history"""
    with db_lock:
        with sqlite3.connect(DB_PATH) as conn:
            conn.execute('''
                INSERT INTO conversations (id, session_id, role, content, provider, model)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (str(uuid.uuid4()), session_id, role, content, provider, model))
            conn.commit()

def get_conversation_history(session_id, limit=None):
    """Get conversation history for a session"""
    with db_lock:
        with sqlite3.connect(DB_PATH) as conn:
            if limit:
                cursor = conn.execute('''
                    SELECT role, content, timestamp FROM conversations
                    WHERE session_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                ''', (session_id, limit))
                # Reverse the order for limited results to get chronological order
                results = cursor.fetchall()
                results.reverse()
            else:
                cursor = conn.execute('''
                    SELECT role, content, timestamp FROM conversations
                    WHERE session_id = ?
                    ORDER BY timestamp ASC
                ''', (session_id,))
                results = cursor.fetchall()

            return [{'role': row[0], 'content': row[1], 'timestamp': row[2]}
                   for row in results]

def get_conversation_summary(session_id):
    """Get conversation summary if it exists"""
    with db_lock:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.execute('''
                SELECT summary FROM conversation_summaries
                WHERE session_id = ?
            ''', (session_id,))
            result = cursor.fetchone()
            return result[0] if result else None

def save_conversation_summary(session_id, summary):
    """Save or update conversation summary"""
    with db_lock:
        with sqlite3.connect(DB_PATH) as conn:
            conn.execute('''
                INSERT OR REPLACE INTO conversation_summaries (session_id, summary)
                VALUES (?, ?)
            ''', (session_id, summary))
            conn.commit()

def build_conversation_context(session_id):
    """Build conversation context for LLM with memory management"""
    history = get_conversation_history(session_id)
    max_length = current_config['max_conversation_length']
    summary_threshold = current_config['conversation_summary_threshold']

    # If conversation is getting long, use summarization
    if len(history) > summary_threshold:
        summary = get_conversation_summary(session_id)
        if not summary and len(history) > 5:
            # Create summary of older messages
            older_messages = history[:-max_length//2]
            summary_text = "Previous conversation summary: "
            for msg in older_messages[-5:]:  # Summarize last 5 older messages
                summary_text += f"{msg['role']}: {msg['content'][:100]}... "
            save_conversation_summary(session_id, summary_text)
            summary = summary_text

        # Use summary + recent messages
        recent_history = history[-max_length//2:]
        if summary:
            context = [{"role": "system", "content": summary}]
            context.extend([{"role": msg['role'], "content": msg['content']}
                          for msg in recent_history])
        else:
            context = [{"role": msg['role'], "content": msg['content']}
                      for msg in history[-max_length:]]
    else:
        # Use full history if it's short enough
        context = [{"role": msg['role'], "content": msg['content']}
                  for msg in history[-max_length:]]

    return context

# Email Functions
def send_email(to_email, subject, body, html_body=None, session_id=None):
    """Send email using SMTP"""
    try:
        # Create message
        msg = MIMEMultipart('alternative')
        msg['From'] = EMAIL_CONFIG['email']
        msg['To'] = to_email
        msg['Subject'] = subject

        # Add text part
        text_part = MIMEText(body, 'plain', 'utf-8')
        msg.attach(text_part)

        # Add HTML part if provided
        if html_body:
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)

        # Connect to SMTP server
        with smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port']) as server:
            if EMAIL_CONFIG['use_tls']:
                server.starttls()
            server.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
            server.send_message(msg)

        # Save to database
        email_id = str(uuid.uuid4())
        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                conn.execute('''
                    INSERT INTO emails (id, subject, sender, recipient, body, html_body,
                                      status, sent_at, session_id, llm_generated)
                    VALUES (?, ?, ?, ?, ?, ?, 'sent', ?, ?, 1)
                ''', (email_id, subject, EMAIL_CONFIG['email'], to_email, body,
                     html_body, datetime.now().isoformat(), session_id))
                conn.commit()

        # Emit real-time notification
        socketio.emit('email_sent', {
            'id': email_id,
            'to': to_email,
            'subject': subject,
            'timestamp': datetime.now().isoformat()
        })

        return {'success': True, 'email_id': email_id, 'message': 'Email enviado com sucesso'}

    except Exception as e:
        return {'success': False, 'error': str(e)}

def check_new_emails():
    """Check for new emails using IMAP"""
    try:
        # Connect to IMAP server
        with imaplib.IMAP4(EMAIL_CONFIG['imap_server'], EMAIL_CONFIG['imap_port']) as mail:
            if EMAIL_CONFIG['use_tls']:
                mail.starttls()
            mail.login(EMAIL_CONFIG['email'], EMAIL_CONFIG['password'])
            mail.select('INBOX')

            # Search for unseen emails
            status, messages = mail.search(None, 'UNSEEN')
            if status != 'OK':
                return []

            new_emails = []
            for msg_id in messages[0].split():
                status, msg_data = mail.fetch(msg_id, '(RFC822)')
                if status != 'OK':
                    continue

                # Parse email
                email_message = email.message_from_bytes(msg_data[0][1])

                # Extract email details
                subject = decode_header(email_message['Subject'])[0][0]
                if isinstance(subject, bytes):
                    subject = subject.decode('utf-8')

                sender = email_message['From']
                recipient = email_message['To']
                message_id = email_message['Message-ID']

                # Get email body
                body = ""
                html_body = None
                if email_message.is_multipart():
                    for part in email_message.walk():
                        if part.get_content_type() == "text/plain":
                            body = part.get_payload(decode=True).decode('utf-8')
                        elif part.get_content_type() == "text/html":
                            html_body = part.get_payload(decode=True).decode('utf-8')
                else:
                    body = email_message.get_payload(decode=True).decode('utf-8')

                # Save to database
                email_id = str(uuid.uuid4())
                with db_lock:
                    with sqlite3.connect(DB_PATH) as conn:
                        conn.execute('''
                            INSERT OR IGNORE INTO emails (id, message_id, subject, sender,
                                                        recipient, body, html_body, status, received_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, 'received', ?)
                        ''', (email_id, message_id, subject, sender, recipient, body,
                             html_body, datetime.now().isoformat()))
                        conn.commit()

                new_emails.append({
                    'id': email_id,
                    'subject': subject,
                    'sender': sender,
                    'body': body[:200] + '...' if len(body) > 200 else body,
                    'timestamp': datetime.now().isoformat()
                })

                # Emit real-time notification
                socketio.emit('email_received', {
                    'id': email_id,
                    'subject': subject,
                    'sender': sender,
                    'preview': body[:100] + '...' if len(body) > 100 else body,
                    'timestamp': datetime.now().isoformat()
                })

            return new_emails

    except Exception as e:
        print(f"Error checking emails: {e}")
        return []

def start_email_monitoring():
    """Start background email monitoring"""
    def monitor_emails():
        while True:
            try:
                check_new_emails()
                time.sleep(30)  # Check every 30 seconds
            except Exception as e:
                print(f"Email monitoring error: {e}")
                time.sleep(60)  # Wait longer on error

    monitor_thread = Thread(target=monitor_emails, daemon=True)
    monitor_thread.start()

# Web Search and Scraping Functions
def tavily_web_search(query, search_type='search', max_results=5):
    """Perform web search using Tavily AI or fallback to DuckDuckGo"""
    try:
        # Try Tavily first
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {TAVILY_CONFIG["api_key"]}'
        }

        payload = {
            'query': query,
            'search_depth': TAVILY_CONFIG['search_depth'],
            'include_answer': True,
            'include_raw_content': True,
            'max_results': max_results,
            'include_domains': TAVILY_CONFIG.get('include_domains', []),
            'exclude_domains': TAVILY_CONFIG.get('exclude_domains', [])
        }

        response = requests.post(TAVILY_CONFIG['base_url'], json=payload, headers=headers, timeout=30)

        if response.status_code == 200:
            data = response.json()

            results = []
            for result in data.get('results', []):
                results.append({
                    'title': result.get('title', ''),
                    'url': result.get('url', ''),
                    'content': result.get('content', ''),
                    'raw_content': result.get('raw_content', ''),
                    'score': result.get('score', 0)
                })

            return {
                'success': True,
                'query': query,
                'answer': data.get('answer', ''),
                'results': results,
                'images': data.get('images', []),
                'follow_up_questions': data.get('follow_up_questions', []),
                'source': 'tavily'
            }
        else:
            # Fallback to DuckDuckGo search
            return duckduckgo_search(query, max_results)

    except Exception as e:
        # Fallback to DuckDuckGo search
        return duckduckgo_search(query, max_results)

def duckduckgo_search(query, max_results=5):
    """Fallback web search using DuckDuckGo"""
    try:
        # DuckDuckGo Instant Answer API
        search_url = "https://api.duckduckgo.com/"
        params = {
            'q': query,
            'format': 'json',
            'no_html': '1',
            'skip_disambig': '1'
        }

        response = requests.get(search_url, params=params, timeout=15)

        if response.status_code == 200:
            data = response.json()

            results = []

            # Add abstract if available
            if data.get('Abstract'):
                results.append({
                    'title': data.get('AbstractText', query),
                    'url': data.get('AbstractURL', ''),
                    'content': data.get('Abstract', ''),
                    'score': 1.0
                })

            # Add related topics
            for topic in data.get('RelatedTopics', [])[:max_results-1]:
                if isinstance(topic, dict) and topic.get('Text'):
                    results.append({
                        'title': topic.get('Text', '')[:100] + '...',
                        'url': topic.get('FirstURL', ''),
                        'content': topic.get('Text', ''),
                        'score': 0.8
                    })

            # If no results, try web scraping of search results
            if not results:
                return scrape_search_results(query, max_results)

            return {
                'success': True,
                'query': query,
                'answer': data.get('Abstract', ''),
                'results': results,
                'images': [],
                'follow_up_questions': [],
                'source': 'duckduckgo'
            }
        else:
            return scrape_search_results(query, max_results)

    except Exception as e:
        return scrape_search_results(query, max_results)

def scrape_search_results(query, max_results=5):
    """Scrape search results as last resort"""
    try:
        # Use DuckDuckGo HTML search
        search_url = f"https://duckduckgo.com/html/?q={query}"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(search_url, headers=headers, timeout=15)

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            results = []
            search_results = soup.find_all('div', class_='result')[:max_results]

            for result in search_results:
                title_elem = result.find('a', class_='result__a')
                snippet_elem = result.find('a', class_='result__snippet')

                if title_elem:
                    title = title_elem.get_text().strip()
                    url = title_elem.get('href', '')
                    content = snippet_elem.get_text().strip() if snippet_elem else ''

                    results.append({
                        'title': title,
                        'url': url,
                        'content': content,
                        'score': 0.6
                    })

            return {
                'success': True,
                'query': query,
                'answer': f'Encontrados {len(results)} resultados para "{query}"',
                'results': results,
                'images': [],
                'follow_up_questions': [],
                'source': 'scraping'
            }
        else:
            return {'success': False, 'error': 'Não foi possível realizar a pesquisa'}

    except Exception as e:
        return {'success': False, 'error': f'Erro na pesquisa: {str(e)}'}

def scrape_website(url, extract_type='full'):
    """Scrape website content"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # Extract title
        title = soup.find('title')
        title_text = title.get_text().strip() if title else 'No title'

        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()

        # Extract content based on type
        if extract_type == 'text':
            content = soup.get_text()
            # Clean up text
            lines = (line.strip() for line in content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            content = ' '.join(chunk for chunk in chunks if chunk)
        elif extract_type == 'articles':
            # Try to find main content areas
            content_selectors = ['article', 'main', '.content', '#content', '.post', '.entry']
            content = ""
            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    content = ' '.join([elem.get_text().strip() for elem in elements])
                    break
            if not content:
                content = soup.get_text()
        else:  # full
            content = str(soup)

        # Extract metadata
        metadata = {
            'description': '',
            'keywords': '',
            'author': '',
            'published_date': ''
        }

        # Meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            metadata['description'] = meta_desc.get('content', '')

        # Meta keywords
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords:
            metadata['keywords'] = meta_keywords.get('content', '')

        # Author
        meta_author = soup.find('meta', attrs={'name': 'author'})
        if meta_author:
            metadata['author'] = meta_author.get('content', '')

        return {
            'success': True,
            'url': url,
            'title': title_text,
            'content': content[:10000],  # Limit content size
            'metadata': metadata,
            'content_length': len(content)
        }

    except Exception as e:
        return {'success': False, 'error': str(e)}

def save_search_result(query, results, search_type='web', session_id=None):
    """Save search results to database"""
    try:
        search_id = str(uuid.uuid4())
        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                conn.execute('''
                    INSERT INTO web_searches (id, query, results, search_type, session_id, results_count)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (search_id, query, json.dumps(results), search_type, session_id, len(results.get('results', []))))
                conn.commit()
        return search_id
    except Exception as e:
        print(f"Error saving search result: {e}")
        return None

def save_scraped_content(url, title, content, metadata, session_id=None):
    """Save scraped content to database"""
    try:
        content_id = str(uuid.uuid4())
        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                conn.execute('''
                    INSERT INTO scraped_content (id, url, title, content, metadata, session_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (content_id, url, title, content, json.dumps(metadata), session_id))
                conn.commit()
        return content_id
    except Exception as e:
        print(f"Error saving scraped content: {e}")
        return None

def get_ollama_response(messages):
    """Fazer requisição para Ollama com contexto de conversa"""
    url = f"http://{current_config['ollama_host']}:{current_config['ollama_port']}/api/generate"

    # Convert conversation context to Ollama format (single prompt)
    prompt = ""
    for msg in messages:
        if msg['role'] == 'system':
            prompt += f"System: {msg['content']}\n"
        elif msg['role'] == 'user':
            prompt += f"User: {msg['content']}\n"
        elif msg['role'] == 'assistant':
            prompt += f"Assistant: {msg['content']}\n"
    prompt += "Assistant: "

    payload = {
        "model": current_config['ollama_model'],
        "prompt": prompt,
        "stream": False
    }
    response = requests.post(url, json=payload, timeout=30)
    if response.status_code == 200:
        return response.json().get('response', 'Erro ao processar resposta')
    else:
        raise Exception(f'Ollama error: {response.status_code}')

def get_openrouter_response(messages):
    """Fazer requisição para OpenRouter com contexto de conversa"""
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {current_config['openrouter_api_key']}",
        "Content-Type": "application/json",
        "HTTP-Referer": "http://localhost:8000",
        "X-Title": "Chat LLM Application"
    }
    payload = {
        "model": current_config['openrouter_model'],
        "messages": messages
    }
    response = requests.post(url, json=payload, headers=headers, timeout=30)
    if response.status_code == 200:
        return response.json()['choices'][0]['message']['content']
    else:
        raise Exception(f'OpenRouter error: {response.status_code} - {response.text}')

def fetch_openrouter_models():
    """Fetch available models from OpenRouter API"""
    try:
        url = "https://openrouter.ai/api/v1/models"
        headers = {
            "Authorization": f"Bearer {current_config['openrouter_api_key']}",
            "Content-Type": "application/json"
        }
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            models_data = response.json()
            models = []
            for model in models_data.get('data', []):
                models.append({
                    'id': model['id'],
                    'name': model.get('name', model['id']),
                    'description': model.get('description', ''),
                    'pricing': model.get('pricing', {}),
                    'context_length': model.get('context_length', 0),
                    'is_free': model.get('pricing', {}).get('prompt', '0') == '0'
                })
            return sorted(models, key=lambda x: (not x['is_free'], x['name']))
        else:
            return []
    except Exception as e:
        print(f"Error fetching OpenRouter models: {e}")
        return []

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/config')
def config():
    return render_template('config.html')

@app.route('/api/config', methods=['GET'])
def get_config():
    return jsonify(current_config)

@app.route('/api/config', methods=['POST'])
def update_config():
    global current_config
    try:
        data = request.get_json()
        current_config.update(data)
        return jsonify({'success': True, 'config': current_config})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/openrouter/models', methods=['GET'])
def get_openrouter_models():
    """Get available OpenRouter models"""
    try:
        models = fetch_openrouter_models()
        return jsonify({'success': True, 'models': models})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/conversation/history', methods=['GET', 'POST'])
def get_conversation():
    """Get conversation history for session"""
    try:
        if request.method == 'POST':
            data = request.get_json()
            session_id = data.get('session_id') if data else None
        else:
            session_id = request.args.get('session_id')

        if not session_id:
            session_id = str(uuid.uuid4())

        history = get_conversation_history(session_id)
        return jsonify({'success': True, 'history': history, 'session_id': session_id})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/conversation/clear', methods=['POST'])
def clear_conversation():
    """Clear conversation history for session"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') if data else None

        if not session_id:
            return jsonify({'success': False, 'error': 'session_id required'}), 400

        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                conn.execute('DELETE FROM conversations WHERE session_id = ?', (session_id,))
                conn.execute('DELETE FROM conversation_summaries WHERE session_id = ?', (session_id,))
                conn.commit()
        return jsonify({'success': True, 'message': 'Conversation cleared'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/conversation/context', methods=['POST'])
def get_conversation_context():
    """Debug endpoint to check conversation context"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') if data else None

        if not session_id:
            return jsonify({'success': False, 'error': 'session_id required'}), 400

        context = build_conversation_context(session_id)
        return jsonify({'success': True, 'context': context, 'session_id': session_id})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debug/database', methods=['GET'])
def debug_database():
    """Debug endpoint to check database contents"""
    try:
        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.execute('SELECT session_id, role, content, timestamp FROM conversations ORDER BY timestamp DESC LIMIT 20')
                results = cursor.fetchall()

                return jsonify({
                    'success': True,
                    'recent_messages': [
                        {'session_id': row[0], 'role': row[1], 'content': row[2][:100] + '...', 'timestamp': row[3]}
                        for row in results
                    ]
                })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        session_id = data.get('session_id')  # Allow client to provide session_id

        if not user_message:
            return jsonify({'error': 'Mensagem não pode estar vazia'}), 400

        # Get or create session ID
        if not session_id:
            session_id = str(uuid.uuid4())

        # Check for special commands
        if user_message.startswith('/search '):
            # Web search command
            query = user_message[8:].strip()
            search_results = tavily_web_search(query, max_results=3)

            if search_results['success']:
                # Save search result
                save_search_result(query, search_results, 'web', session_id)

                # Format response
                bot_message = f"🔍 **Pesquisa Web: {query}**\n\n"

                if search_results.get('answer'):
                    bot_message += f"**Resposta Direta:**\n{search_results['answer']}\n\n"

                bot_message += "**Resultados Encontrados:**\n"
                for i, result in enumerate(search_results['results'][:3], 1):
                    bot_message += f"{i}. **{result['title']}**\n"
                    bot_message += f"   {result['url']}\n"
                    bot_message += f"   {result['content'][:200]}...\n\n"

                if search_results.get('follow_up_questions'):
                    bot_message += "**Perguntas Relacionadas:**\n"
                    for question in search_results['follow_up_questions'][:3]:
                        bot_message += f"• {question}\n"
            else:
                bot_message = f"❌ Erro na pesquisa: {search_results.get('error', 'Erro desconhecido')}"

        elif user_message.startswith('/scrape '):
            # Web scraping command
            url = user_message[8:].strip()
            scrape_result = scrape_website(url, 'text')

            if scrape_result['success']:
                # Save scraped content
                save_scraped_content(url, scrape_result['title'], scrape_result['content'],
                                   scrape_result['metadata'], session_id)

                bot_message = f"🌐 **Conteúdo Extraído de: {url}**\n\n"
                bot_message += f"**Título:** {scrape_result['title']}\n\n"
                bot_message += f"**Conteúdo:** {scrape_result['content'][:1000]}...\n\n"
                bot_message += f"**Tamanho Total:** {scrape_result['content_length']} caracteres"
            else:
                bot_message = f"❌ Erro ao extrair conteúdo: {scrape_result.get('error', 'Erro desconhecido')}"

        else:
            # Regular chat with AI
            # Save user message
            save_message(session_id, 'user', user_message,
                        current_config['provider'], current_config.get(f"{current_config['provider']}_model"))

            # Build conversation context with memory
            conversation_context = build_conversation_context(session_id)

            # Check if user is asking for web search
            search_keywords = ['pesquise', 'busque', 'procure', 'search', 'find', 'what is', 'o que é', 'como está', 'notícias', 'news']
            if any(keyword in user_message.lower() for keyword in search_keywords):
                # Enhance with web search
                search_query = user_message
                search_results = tavily_web_search(search_query, max_results=2)

                if search_results['success']:
                    # Add search context to conversation
                    search_context = f"Informações atuais da web sobre '{search_query}':\n"
                    for result in search_results['results'][:2]:
                        search_context += f"- {result['title']}: {result['content'][:300]}...\n"

                    conversation_context.append({"role": "system", "content": search_context})

            conversation_context.append({"role": "user", "content": user_message})

            # Debug: Log conversation context (remove in production)
            print(f"DEBUG: Session {session_id} - Conversation context: {conversation_context}")

            # Get response from chosen provider
            if current_config['provider'] == 'ollama':
                bot_message = get_ollama_response(conversation_context)
            elif current_config['provider'] == 'openrouter':
                bot_message = get_openrouter_response(conversation_context)
            else:
                return jsonify({'error': 'Provedor não configurado'}), 400

        # Save assistant response
        save_message(session_id, 'assistant', bot_message,
                    current_config['provider'], current_config.get(f"{current_config['provider']}_model"))

        return jsonify({
            'success': True,
            'message': bot_message,
            'provider': current_config['provider'],
            'session_id': session_id,
            'conversation_length': len(get_conversation_history(session_id))
        })

    except requests.exceptions.RequestException as e:
        return jsonify({
            'success': False,
            'error': f'Erro de conexão: {str(e)}'
        }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Erro interno: {str(e)}'
        }), 500

@app.route('/api/test-provider', methods=['POST'])
def test_provider():
    """Testar conectividade de um provedor específico"""
    try:
        data = request.get_json()
        provider = data.get('provider')

        if provider == 'ollama':
            # Testar Ollama
            url = f"http://{data.get('ollama_host', 'host.docker.internal')}:{data.get('ollama_port', '11434')}/api/generate"
            payload = {
                "model": data.get('ollama_model', 'llama3.2:1b'),
                "prompt": "test",
                "stream": False
            }
            response = requests.post(url, json=payload, timeout=10)
            status = "online" if response.status_code == 200 else "offline"

        elif provider == 'openrouter':
            # Testar OpenRouter
            url = "https://openrouter.ai/api/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {data.get('openrouter_api_key', '')}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": data.get('openrouter_model', 'google/gemma-2-9b-it:free'),
                "messages": [{"role": "user", "content": "test"}],
                "max_tokens": 10
            }
            response = requests.post(url, json=payload, headers=headers, timeout=10)
            status = "online" if response.status_code == 200 else "offline"
        else:
            return jsonify({'success': False, 'error': 'Provedor inválido'}), 400

        return jsonify({
            'success': True,
            'status': status,
            'provider': provider,
            'response_code': response.status_code
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'status': 'offline',
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health():
    """Status geral da aplicação"""
    # Testar provedor atual
    current_status = "offline"
    try:
        if current_config['provider'] == 'ollama':
            current_status = get_ollama_response("test")[:10] + "..." if get_ollama_response("test") else "online"
        elif current_config['provider'] == 'openrouter':
            current_status = get_openrouter_response("test")[:10] + "..." if get_openrouter_response("test") else "online"
        current_status = "online"
    except:
        current_status = "offline"

    return jsonify({
        'status': 'online',
        'current_provider': current_config['provider'],
        'provider_status': current_status,
        'config': current_config
    })

# Email Routes
@app.route('/email')
def email_interface():
    return render_template('email.html')

@app.route('/api/email/compose', methods=['POST'])
def compose_email():
    """Generate email content using LLM"""
    try:
        data = request.get_json()
        prompt = data.get('prompt', '')
        session_id = data.get('session_id')

        if not prompt:
            return jsonify({'error': 'Prompt não pode estar vazio'}), 400

        # Create email composition prompt
        email_prompt = f"""
        Você é um assistente especializado em escrever emails profissionais.

        TAREFA: {prompt}

        INSTRUÇÕES:
        - Escreva um email completo e profissional
        - Use linguagem formal e cortês
        - Inclua um assunto claro e objetivo
        - O corpo deve ser bem estruturado
        - Assine como "Equipe Grupo Alves"

        FORMATO OBRIGATÓRIO:
        ASSUNTO: [escreva o assunto aqui]

        CORPO:
        [escreva o corpo do email aqui]

        Responda APENAS no formato acima, sem explicações adicionais.
        """

        # Get response from LLM
        if not session_id:
            session_id = str(uuid.uuid4())

        # Build conversation context
        conversation_context = build_conversation_context(session_id)
        conversation_context.append({"role": "user", "content": email_prompt})

        # Get response from chosen provider
        if current_config['provider'] == 'ollama':
            llm_response = get_ollama_response(conversation_context)
        elif current_config['provider'] == 'openrouter':
            llm_response = get_openrouter_response(conversation_context)
        else:
            return jsonify({'error': 'Provedor não configurado'}), 400

        # Parse LLM response
        subject = ""
        body = ""
        html_body = ""

        # Try to extract subject
        if 'ASSUNTO:' in llm_response:
            subject_start = llm_response.find('ASSUNTO:') + len('ASSUNTO:')
            subject_end = llm_response.find('\n', subject_start)
            if subject_end == -1:
                subject_end = llm_response.find('CORPO:', subject_start)
            if subject_end != -1:
                subject = llm_response[subject_start:subject_end].strip()

        # Try to extract body
        if 'CORPO:' in llm_response:
            body_start = llm_response.find('CORPO:') + len('CORPO:')
            body_end = llm_response.find('HTML:', body_start)
            if body_end == -1:
                body_end = len(llm_response)
            body = llm_response[body_start:body_end].strip()

        # Try to extract HTML
        if 'HTML:' in llm_response:
            html_start = llm_response.find('HTML:') + len('HTML:')
            html_body = llm_response[html_start:].strip()
            if not html_body:
                html_body = None

        # Fallback if parsing failed
        if not subject:
            subject = "Email gerado por IA"
        if not body:
            body = llm_response.strip()

        return jsonify({
            'success': True,
            'subject': subject,
            'body': body,
            'html_body': html_body,
            'session_id': session_id
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/send', methods=['POST'])
def send_email_api():
    """Send email"""
    try:
        data = request.get_json()
        to_email = data.get('to_email', '')
        subject = data.get('subject', '')
        body = data.get('body', '')
        html_body = data.get('html_body')
        session_id = data.get('session_id')

        if not to_email or not subject or not body:
            return jsonify({'error': 'Email, assunto e corpo são obrigatórios'}), 400

        result = send_email(to_email, subject, body, html_body, session_id)
        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/list', methods=['GET'])
def list_emails():
    """List emails from database"""
    try:
        email_type = request.args.get('type', 'all')  # sent, received, all
        limit = int(request.args.get('limit', 50))

        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                if email_type == 'sent':
                    cursor = conn.execute('''
                        SELECT id, subject, recipient as contact, body, status, sent_at as timestamp, llm_generated
                        FROM emails WHERE status = 'sent'
                        ORDER BY sent_at DESC LIMIT ?
                    ''', (limit,))
                elif email_type == 'received':
                    cursor = conn.execute('''
                        SELECT id, subject, sender as contact, body, status, received_at as timestamp, 0 as llm_generated
                        FROM emails WHERE status = 'received'
                        ORDER BY received_at DESC LIMIT ?
                    ''', (limit,))
                else:
                    cursor = conn.execute('''
                        SELECT id, subject,
                               CASE WHEN status = 'sent' THEN recipient ELSE sender END as contact,
                               body, status,
                               CASE WHEN status = 'sent' THEN sent_at ELSE received_at END as timestamp,
                               llm_generated
                        FROM emails
                        ORDER BY timestamp DESC LIMIT ?
                    ''', (limit,))

                emails = []
                for row in cursor.fetchall():
                    emails.append({
                        'id': row[0],
                        'subject': row[1],
                        'contact': row[2],
                        'preview': row[3][:100] + '...' if len(row[3]) > 100 else row[3],
                        'status': row[4],
                        'timestamp': row[5],
                        'llm_generated': bool(row[6])
                    })

                return jsonify({'success': True, 'emails': emails})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/<email_id>', methods=['GET'])
def get_email(email_id):
    """Get specific email details"""
    try:
        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.execute('''
                    SELECT id, subject, sender, recipient, body, html_body, status,
                           created_at, sent_at, received_at, llm_generated
                    FROM emails WHERE id = ?
                ''', (email_id,))

                row = cursor.fetchone()
                if not row:
                    return jsonify({'error': 'Email não encontrado'}), 404

                email_data = {
                    'id': row[0],
                    'subject': row[1],
                    'sender': row[2],
                    'recipient': row[3],
                    'body': row[4],
                    'html_body': row[5],
                    'status': row[6],
                    'created_at': row[7],
                    'sent_at': row[8],
                    'received_at': row[9],
                    'llm_generated': bool(row[10])
                }

                return jsonify({'success': True, 'email': email_data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/check', methods=['POST'])
def check_emails_api():
    """Manually check for new emails"""
    try:
        new_emails = check_new_emails()
        return jsonify({'success': True, 'new_emails': new_emails, 'count': len(new_emails)})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Web Search and Scraping Routes
@app.route('/search')
def search_interface():
    return render_template('search.html')

@app.route('/api/search/web', methods=['POST'])
def web_search_api():
    """Perform web search using Tavily AI"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        max_results = data.get('max_results', 5)
        session_id = data.get('session_id')

        if not query:
            return jsonify({'error': 'Query não pode estar vazia'}), 400

        # Perform search
        results = tavily_web_search(query, max_results=max_results)

        if results['success']:
            # Save to database
            search_id = save_search_result(query, results, 'web', session_id)
            results['search_id'] = search_id

            # Emit real-time notification
            socketio.emit('search_completed', {
                'query': query,
                'results_count': len(results.get('results', [])),
                'search_id': search_id
            })

        return jsonify(results)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/search/scrape', methods=['POST'])
def scrape_website_api():
    """Scrape website content"""
    try:
        data = request.get_json()
        url = data.get('url', '')
        extract_type = data.get('extract_type', 'full')
        session_id = data.get('session_id')

        if not url:
            return jsonify({'error': 'URL não pode estar vazia'}), 400

        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return jsonify({'error': 'URL inválida'}), 400

        # Perform scraping
        result = scrape_website(url, extract_type)

        if result['success']:
            # Save to database
            content_id = save_scraped_content(
                url, result['title'], result['content'],
                result['metadata'], session_id
            )
            result['content_id'] = content_id

            # Emit real-time notification
            socketio.emit('scrape_completed', {
                'url': url,
                'title': result['title'],
                'content_length': result['content_length'],
                'content_id': content_id
            })

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/search/history', methods=['GET'])
def search_history():
    """Get search history"""
    try:
        session_id = request.args.get('session_id')
        search_type = request.args.get('type', 'all')  # web, scrape, all
        limit = int(request.args.get('limit', 50))

        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                if search_type == 'web':
                    cursor = conn.execute('''
                        SELECT id, query, results_count, timestamp FROM web_searches
                        WHERE session_id = ? OR session_id IS NULL
                        ORDER BY timestamp DESC LIMIT ?
                    ''', (session_id, limit))

                    searches = []
                    for row in cursor.fetchall():
                        searches.append({
                            'id': row[0],
                            'query': row[1],
                            'results_count': row[2],
                            'timestamp': row[3],
                            'type': 'web'
                        })

                elif search_type == 'scrape':
                    cursor = conn.execute('''
                        SELECT id, url, title, timestamp FROM scraped_content
                        WHERE session_id = ? OR session_id IS NULL
                        ORDER BY timestamp DESC LIMIT ?
                    ''', (session_id, limit))

                    searches = []
                    for row in cursor.fetchall():
                        searches.append({
                            'id': row[0],
                            'url': row[1],
                            'title': row[2],
                            'timestamp': row[3],
                            'type': 'scrape'
                        })
                else:  # all
                    # Get both web searches and scraped content
                    web_cursor = conn.execute('''
                        SELECT id, query as title, results_count, timestamp, 'web' as type FROM web_searches
                        WHERE session_id = ? OR session_id IS NULL
                        ORDER BY timestamp DESC LIMIT ?
                    ''', (session_id, limit//2))

                    scrape_cursor = conn.execute('''
                        SELECT id, title, 1 as results_count, timestamp, 'scrape' as type FROM scraped_content
                        WHERE session_id = ? OR session_id IS NULL
                        ORDER BY timestamp DESC LIMIT ?
                    ''', (session_id, limit//2))

                    searches = []
                    for row in web_cursor.fetchall():
                        searches.append({
                            'id': row[0],
                            'title': row[1],
                            'results_count': row[2],
                            'timestamp': row[3],
                            'type': row[4]
                        })

                    for row in scrape_cursor.fetchall():
                        searches.append({
                            'id': row[0],
                            'title': row[1],
                            'results_count': row[2],
                            'timestamp': row[3],
                            'type': row[4]
                        })

                    # Sort by timestamp
                    searches.sort(key=lambda x: x['timestamp'], reverse=True)

                return jsonify({'success': True, 'searches': searches})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/search/<search_id>', methods=['GET'])
def get_search_result(search_id):
    """Get specific search result"""
    try:
        with db_lock:
            with sqlite3.connect(DB_PATH) as conn:
                # Try web searches first
                cursor = conn.execute('''
                    SELECT query, results, search_type, timestamp FROM web_searches WHERE id = ?
                ''', (search_id,))

                row = cursor.fetchone()
                if row:
                    return jsonify({
                        'success': True,
                        'type': 'web',
                        'query': row[0],
                        'results': json.loads(row[1]),
                        'search_type': row[2],
                        'timestamp': row[3]
                    })

                # Try scraped content
                cursor = conn.execute('''
                    SELECT url, title, content, metadata, timestamp FROM scraped_content WHERE id = ?
                ''', (search_id,))

                row = cursor.fetchone()
                if row:
                    return jsonify({
                        'success': True,
                        'type': 'scrape',
                        'url': row[0],
                        'title': row[1],
                        'content': row[2],
                        'metadata': json.loads(row[3]) if row[3] else {},
                        'timestamp': row[4]
                    })

                return jsonify({'error': 'Resultado não encontrado'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# WebSocket Events
@socketio.on('connect')
def handle_connect():
    print('Client connected')

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

if __name__ == '__main__':
    # Start email monitoring
    start_email_monitoring()

    # Run the app with SocketIO
    socketio.run(app, host='0.0.0.0', port=8000, debug=True, allow_unsafe_werkzeug=True)
