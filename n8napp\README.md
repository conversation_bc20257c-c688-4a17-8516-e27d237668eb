# N8N com Serviço Echo - Setup Docker

Este projeto configura um ambiente Docker com n8n e um serviço de echo para testes básicos.

## Serviços Incluídos

- **n8n**: Plataforma de automação de workflows
- **echo-service**: Serviço HTTP que retorna as requisições recebidas (útil para testes)

## Como Usar

### 1. Iniciar os Serviços

```bash
docker-compose up -d
```

### 2. Acessar o n8n

- URL: http://localhost:5678
- Usuário: admin
- Senha: admin123

### 3. Testar o Serviço Echo

O serviço echo estará disponível em: http://localhost:8080

Você pode testar fazendo uma requisição:

```bash
curl -X POST http://localhost:8080/test \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello World"}'
```

### 4. Criar Workflow no n8n

1. Acesse o n8n em http://localhost:5678
2. Crie um novo workflow
3. Adicione um nó "HTTP Request" apontando para http://echo-service:8080
4. Configure o método e dados conforme necessário
5. Execute o workflow para testar

## Estrutura do Projeto

```
n8napp/
├── docker-compose.yml    # Configuração dos serviços Docker
├── workflows/           # Diretório para workflows do n8n
└── README.md           # Este arquivo
```

## Comandos Úteis

```bash
# Iniciar serviços
docker-compose up -d

# Ver logs
docker-compose logs -f

# Parar serviços
docker-compose down

# Parar e remover volumes (limpa dados)
docker-compose down -v
```

## Portas Utilizadas

- 5678: n8n Web Interface
- 8080: Echo Service HTTP

## Volumes

- `n8n_data`: Dados persistentes do n8n
- `./workflows`: Workflows do n8n (mapeado para o host)
