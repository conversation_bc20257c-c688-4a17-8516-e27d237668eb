<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Email com IA - Grupo Alves</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .email-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: grid;
            grid-template-columns: 300px 1fr;
            height: 90vh;
        }

        .sidebar {
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
        }

        .nav-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 1px solid #e0e0e0;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-item:hover, .nav-item.active {
            background: #e3f2fd;
            color: #1976d2;
        }

        .main-content {
            display: flex;
            flex-direction: column;
        }

        .content-header {
            background: white;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .content-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .compose-section {
            display: none;
        }

        .compose-section.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .email-list {
            display: none;
        }

        .email-list.active {
            display: block;
        }

        .email-item {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            transition: background 0.3s;
        }

        .email-item:hover {
            background: #f5f5f5;
        }

        .email-subject {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .email-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .email-preview {
            font-size: 14px;
            color: #888;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            display: none;
        }

        .notification.error {
            background: #f44336;
        }

        .notification.show {
            display: block;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            display: inline-block;
            margin-right: 8px;
        }

        .status-indicator.online {
            background: #44ff44;
        }

        .llm-badge {
            background: #667eea;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .email-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .email-modal.show {
            display: flex;
        }

        .email-modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 900px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            width: 90%;
        }

        .email-header-full {
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }

        .email-content {
            line-height: 1.6;
            margin-bottom: 30px;
            white-space: pre-wrap;
        }

        .reply-suggestions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .suggestion-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .suggestion-item:hover {
            border-color: #667eea;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1);
        }

        .suggestion-tone {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 8px;
        }

        .suggestion-subject {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .suggestion-body {
            color: #666;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>📧 Email IA</h2>
                <p>Grupo Alves</p>
            </div>
            <div class="sidebar-nav">
                <div class="nav-item active" onclick="showSection('compose')">
                    ✏️ Compor Email
                </div>
                <div class="nav-item" onclick="showSection('inbox')">
                    📥 Caixa de Entrada
                </div>
                <div class="nav-item" onclick="showSection('sent')">
                    📤 Enviados
                </div>
                <div class="nav-item" onclick="showSection('dashboard')">
                    📊 Dashboard
                </div>
                <div class="nav-item" onclick="checkEmails()">
                    🔄 Verificar Emails
                </div>
                <div class="nav-item">
                    <span class="status-indicator" id="emailStatus"></span>
                    Status: <span id="statusText">Offline</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="content-header">
                <h1 id="sectionTitle">Compor Email com IA</h1>
                <div>
                    <a href="/util-tools" class="btn btn-primary">🛠️ UTIL Tools</a>
                    <a href="/chat" class="btn btn-secondary">💬 Chat</a>
                    <a href="/help" class="btn btn-secondary">❓ Ajuda</a>
                </div>
            </div>

            <div class="content-body">
                <!-- Compose Section -->
                <div id="compose" class="compose-section active">
                    <div class="form-group">
                        <label class="form-label">Descreva o email que deseja enviar:</label>
                        <textarea class="form-textarea" id="emailPrompt" placeholder="Ex: Escreva um email profissional para um cliente agradecendo pela compra e oferecendo suporte..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="generateEmail()">🤖 Gerar Email com IA</button>
                    </div>

                    <div id="generatedEmail" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">Para:</label>
                            <input type="email" class="form-input" id="toEmail" placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Assunto:</label>
                            <input type="text" class="form-input" id="emailSubject">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Corpo do Email:</label>
                            <textarea class="form-textarea" id="emailBody" rows="10"></textarea>
                        </div>

                        <div class="form-group">
                            <button class="btn btn-primary" onclick="sendEmail()">📧 Enviar Email</button>
                            <button class="btn btn-secondary" onclick="regenerateEmail()">🔄 Regenerar</button>
                        </div>
                    </div>
                </div>

                <!-- Email Lists -->
                <div id="inbox" class="email-list">
                    <div class="loading" id="inboxLoading">Carregando emails recebidos...</div>
                    <div id="inboxEmails"></div>
                </div>

                <div id="sent" class="email-list">
                    <div class="loading" id="sentLoading">Carregando emails enviados...</div>
                    <div id="sentEmails"></div>
                </div>

                <!-- Dashboard -->
                <div id="dashboard" class="email-list">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="totalSent">0</div>
                            <div class="stat-label">Emails Enviados</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalReceived">0</div>
                            <div class="stat-label">Emails Recebidos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="llmGenerated">0</div>
                            <div class="stat-label">Gerados por IA</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="todayCount">0</div>
                            <div class="stat-label">Hoje</div>
                        </div>
                    </div>
                    <div id="recentActivity"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <!-- Email Reading Modal -->
    <div class="email-modal" id="emailModal">
        <div class="email-modal-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>📧 Email</h2>
                <button onclick="closeEmailModal()" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
            </div>

            <div class="email-header-full" id="emailHeaderFull">
                <!-- Email header will be populated here -->
            </div>

            <div class="email-content" id="emailContentFull">
                <!-- Email content will be populated here -->
            </div>

            <div style="border-top: 1px solid #e0e0e0; padding-top: 20px;">
                <button class="btn btn-primary" onclick="generateReplyForEmail()" id="generateReplyBtn">🤖 Gerar Resposta com IA</button>
                <button class="btn btn-secondary" onclick="analyzeEmailSentiment()" id="analyzeBtn">📊 Analisar Sentimento</button>
                <button class="btn btn-secondary" onclick="closeEmailModal()">Fechar</button>
            </div>

            <div class="reply-suggestions" id="replySuggestions" style="display: none;">
                <h3>🤖 Sugestões de Resposta Geradas por IA</h3>
                <div id="suggestionsContainer">
                    <!-- Suggestions will be populated here -->
                </div>
            </div>

            <div id="sentimentAnalysis" style="display: none; background: #f0f8ff; padding: 15px; border-radius: 8px; margin-top: 20px;">
                <h4>📊 Análise de Sentimento</h4>
                <div id="sentimentResults">
                    <!-- Sentiment analysis will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        let currentSessionId = localStorage.getItem('email_session_id') || generateUUID();
        localStorage.setItem('email_session_id', currentSessionId);

        // Socket event listeners
        socket.on('email_sent', function(data) {
            showNotification(`Email enviado para ${data.to}`, 'success');
            if (document.getElementById('sent').classList.contains('active')) {
                loadEmails('sent');
            }
            updateDashboard();
        });

        socket.on('email_received', function(data) {
            showNotification(`Novo email de ${data.sender}: ${data.subject}`, 'success');
            if (document.getElementById('inbox').classList.contains('active')) {
                loadEmails('received');
            }
            updateDashboard();
        });

        // Utility functions
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }

        // Navigation
        function showSection(section) {
            // Update nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update content
            document.querySelectorAll('.compose-section, .email-list').forEach(el => {
                el.classList.remove('active');
            });
            document.getElementById(section).classList.add('active');

            // Update title
            const titles = {
                'compose': 'Compor Email com IA',
                'inbox': 'Caixa de Entrada',
                'sent': 'Emails Enviados',
                'dashboard': 'Dashboard'
            };
            document.getElementById('sectionTitle').textContent = titles[section];

            // Load data if needed
            if (section === 'inbox') {
                loadEmails('received');
            } else if (section === 'sent') {
                loadEmails('sent');
            } else if (section === 'dashboard') {
                updateDashboard();
            }
        }

        // Email generation
        async function generateEmail() {
            const prompt = document.getElementById('emailPrompt').value.trim();
            if (!prompt) {
                showNotification('Por favor, descreva o email que deseja gerar', 'error');
                return;
            }

            try {
                const response = await fetch('/api/email/compose', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        session_id: currentSessionId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('emailSubject').value = data.subject;
                    document.getElementById('emailBody').value = data.body;
                    document.getElementById('generatedEmail').style.display = 'block';
                    showNotification('Email gerado com sucesso!', 'success');
                } else {
                    showNotification('Erro ao gerar email: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Erro de conexão: ' + error.message, 'error');
            }
        }

        // Send email
        async function sendEmail() {
            const toEmail = document.getElementById('toEmail').value.trim();
            const subject = document.getElementById('emailSubject').value.trim();
            const body = document.getElementById('emailBody').value.trim();

            if (!toEmail || !subject || !body) {
                showNotification('Todos os campos são obrigatórios', 'error');
                return;
            }

            try {
                const response = await fetch('/api/email/send', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        to_email: toEmail,
                        subject: subject,
                        body: body,
                        session_id: currentSessionId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('Email enviado com sucesso!', 'success');
                    // Clear form
                    document.getElementById('emailPrompt').value = '';
                    document.getElementById('generatedEmail').style.display = 'none';
                } else {
                    showNotification('Erro ao enviar email: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Erro de conexão: ' + error.message, 'error');
            }
        }

        // Regenerate email
        function regenerateEmail() {
            generateEmail();
        }

        // Load emails
        async function loadEmails(type) {
            const loadingId = type === 'received' ? 'inboxLoading' : 'sentLoading';
            const containerId = type === 'received' ? 'inboxEmails' : 'sentEmails';
            
            document.getElementById(loadingId).style.display = 'block';

            try {
                const response = await fetch(`/api/email/list?type=${type}&limit=50`);
                const data = await response.json();

                if (data.success) {
                    const container = document.getElementById(containerId);
                    container.innerHTML = '';

                    data.emails.forEach(email => {
                        const emailDiv = document.createElement('div');
                        emailDiv.className = 'email-item';
                        emailDiv.onclick = () => openEmailModal(email);
                        emailDiv.innerHTML = `
                            <div class="email-subject">
                                ${email.subject}
                                ${email.llm_generated ? '<span class="llm-badge">IA</span>' : ''}
                            </div>
                            <div class="email-meta">
                                ${type === 'received' ? 'De: ' + email.contact : 'Para: ' + email.contact} •
                                ${new Date(email.timestamp).toLocaleString('pt-BR')}
                            </div>
                            <div class="email-preview">${email.preview}</div>
                        `;
                        container.appendChild(emailDiv);
                    });

                    if (data.emails.length === 0) {
                        container.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">Nenhum email encontrado</p>';
                    }
                }
            } catch (error) {
                showNotification('Erro ao carregar emails: ' + error.message, 'error');
            } finally {
                document.getElementById(loadingId).style.display = 'none';
            }
        }

        // Check for new emails
        async function checkEmails() {
            try {
                const response = await fetch('/api/email/check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(`Verificação concluída. ${data.count} novos emails encontrados.`, 'success');
                    if (document.getElementById('inbox').classList.contains('active')) {
                        loadEmails('received');
                    }
                } else {
                    showNotification('Erro ao verificar emails: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Erro de conexão: ' + error.message, 'error');
            }
        }

        // Update dashboard
        async function updateDashboard() {
            try {
                const [sentResponse, receivedResponse] = await Promise.all([
                    fetch('/api/email/list?type=sent&limit=1000'),
                    fetch('/api/email/list?type=received&limit=1000')
                ]);

                const sentData = await sentResponse.json();
                const receivedData = await receivedResponse.json();

                if (sentData.success && receivedData.success) {
                    const sentEmails = sentData.emails;
                    const receivedEmails = receivedData.emails;
                    
                    document.getElementById('totalSent').textContent = sentEmails.length;
                    document.getElementById('totalReceived').textContent = receivedEmails.length;
                    
                    const llmCount = sentEmails.filter(email => email.llm_generated).length;
                    document.getElementById('llmGenerated').textContent = llmCount;
                    
                    const today = new Date().toDateString();
                    const todayEmails = [...sentEmails, ...receivedEmails].filter(email => 
                        new Date(email.timestamp).toDateString() === today
                    );
                    document.getElementById('todayCount').textContent = todayEmails.length;
                }
            } catch (error) {
                console.error('Error updating dashboard:', error);
            }
        }

        // Check email service status
        async function checkEmailStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                const statusIndicator = document.getElementById('emailStatus');
                const statusText = document.getElementById('statusText');
                
                if (data.status === 'online') {
                    statusIndicator.classList.add('online');
                    statusText.textContent = 'Online';
                } else {
                    statusIndicator.classList.remove('online');
                    statusText.textContent = 'Offline';
                }
            } catch (error) {
                document.getElementById('emailStatus').classList.remove('online');
                document.getElementById('statusText').textContent = 'Erro';
            }
        }

        // Email reading and AI reply functions
        let currentEmailData = null;

        function openEmailModal(email) {
            currentEmailData = email;
            const modal = document.getElementById('emailModal');
            const header = document.getElementById('emailHeaderFull');
            const content = document.getElementById('emailContentFull');

            header.innerHTML = `
                <h3>${email.subject}</h3>
                <div style="margin: 10px 0; color: #666;">
                    <strong>De:</strong> ${email.contact || email.sender || 'N/A'}<br>
                    <strong>Para:</strong> ${email.recipient || 'Você'}<br>
                    <strong>Data:</strong> ${new Date(email.timestamp).toLocaleString('pt-BR')}<br>
                    ${email.llm_generated ? '<span class="llm-badge">Gerado por IA</span>' : ''}
                </div>
            `;

            content.innerHTML = email.body || email.content || 'Conteúdo não disponível';

            // Reset suggestions and analysis
            document.getElementById('replySuggestions').style.display = 'none';
            document.getElementById('sentimentAnalysis').style.display = 'none';

            modal.classList.add('show');
        }

        function closeEmailModal() {
            document.getElementById('emailModal').classList.remove('show');
            currentEmailData = null;
        }

        async function generateReplyForEmail() {
            if (!currentEmailData) {
                showNotification('Nenhum email selecionado', 'error');
                return;
            }

            const generateBtn = document.getElementById('generateReplyBtn');
            generateBtn.disabled = true;
            generateBtn.textContent = '🤖 Gerando...';

            try {
                const response = await fetch(`/api/email/${currentEmailData.id}/reply-suggestions`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success && data.suggestions) {
                    displayReplySuggestions(data.suggestions);
                    showNotification('Sugestões de resposta geradas!', 'success');
                } else {
                    showNotification('Erro ao gerar sugestões: ' + (data.error || 'Erro desconhecido'), 'error');
                }
            } catch (error) {
                showNotification('Erro de conexão: ' + error.message, 'error');
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '🤖 Gerar Resposta com IA';
            }
        }

        function displayReplySuggestions(suggestions) {
            const container = document.getElementById('suggestionsContainer');
            const replySuggestions = document.getElementById('replySuggestions');

            container.innerHTML = '';

            suggestions.forEach((suggestion, index) => {
                const suggestionDiv = document.createElement('div');
                suggestionDiv.className = 'suggestion-item';
                suggestionDiv.onclick = () => useSuggestion(suggestion);

                suggestionDiv.innerHTML = `
                    <div class="suggestion-tone">${suggestion.tone}</div>
                    <div class="suggestion-subject">${suggestion.subject}</div>
                    <div class="suggestion-body">${suggestion.body}</div>
                `;

                container.appendChild(suggestionDiv);
            });

            replySuggestions.style.display = 'block';
        }

        function useSuggestion(suggestion) {
            // Copy to clipboard and optionally redirect to compose
            const emailText = `Assunto: ${suggestion.subject}\n\n${suggestion.body}`;

            navigator.clipboard.writeText(emailText).then(() => {
                showNotification('Sugestão copiada para a área de transferência!', 'success');

                // Optionally fill compose form
                if (confirm('Deseja usar esta sugestão no formulário de composição?')) {
                    document.getElementById('emailSubject').value = suggestion.subject;
                    document.getElementById('emailBody').value = suggestion.body;

                    // Set recipient if available
                    if (currentEmailData && currentEmailData.sender) {
                        document.getElementById('toEmail').value = currentEmailData.sender;
                    }

                    document.getElementById('generatedEmail').style.display = 'block';
                    closeEmailModal();
                    showSection('compose');
                }
            }).catch(() => {
                showNotification('Erro ao copiar para área de transferência', 'error');
            });
        }

        async function analyzeEmailSentiment() {
            if (!currentEmailData) {
                showNotification('Nenhum email selecionado', 'error');
                return;
            }

            const analyzeBtn = document.getElementById('analyzeBtn');
            analyzeBtn.disabled = true;
            analyzeBtn.textContent = '📊 Analisando...';

            try {
                const response = await fetch(`/api/email/${currentEmailData.id}/analyze`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success && data.analysis) {
                    displaySentimentAnalysis(data.analysis);
                    showNotification('Análise de sentimento concluída!', 'success');
                } else {
                    showNotification('Erro na análise: ' + (data.error || 'Erro desconhecido'), 'error');
                }
            } catch (error) {
                showNotification('Erro de conexão: ' + error.message, 'error');
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = '📊 Analisar Sentimento';
            }
        }

        function displaySentimentAnalysis(analysis) {
            const container = document.getElementById('sentimentResults');
            const sentimentAnalysis = document.getElementById('sentimentAnalysis');

            const sentimentColor = analysis.sentiment_score > 0.1 ? '#4caf50' :
                                  analysis.sentiment_score < -0.1 ? '#f44336' : '#ffc107';

            const sentimentLabel = analysis.sentiment_score > 0.1 ? 'Positivo' :
                                  analysis.sentiment_score < -0.1 ? 'Negativo' : 'Neutro';

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: ${sentimentColor};">
                            ${(analysis.sentiment_score * 100).toFixed(0)}%
                        </div>
                        <div style="font-size: 14px; color: #666;">Sentimento: ${sentimentLabel}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 18px; font-weight: bold; color: #667eea;">
                            ${analysis.tone || 'N/A'}
                        </div>
                        <div style="font-size: 14px; color: #666;">Tom da Comunicação</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 18px; font-weight: bold; color: #ff9800;">
                            ${analysis.urgency_level || 3}/5
                        </div>
                        <div style="font-size: 14px; color: #666;">Nível de Urgência</div>
                    </div>
                </div>

                ${analysis.key_topics && analysis.key_topics.length > 0 ? `
                    <div style="margin-top: 15px;">
                        <strong>Tópicos Principais:</strong><br>
                        <div style="margin-top: 8px;">
                            ${analysis.key_topics.map(topic => `<span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">${topic}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}
            `;

            sentimentAnalysis.style.display = 'block';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkEmailStatus();
            updateDashboard();

            // Check status every 30 seconds
            setInterval(checkEmailStatus, 30000);
        });

        // Close modal when clicking outside
        document.getElementById('emailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEmailModal();
            }
        });
    </script>
</body>
</html>
